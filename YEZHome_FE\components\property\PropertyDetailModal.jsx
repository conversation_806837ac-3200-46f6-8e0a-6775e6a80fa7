"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChevronLeft, Heart, Share, X, Send } from "lucide-react";
import Image from "next/image";
import dynamic from "next/dynamic";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { addToFavorites, removeFromFavorites } from "@/app/actions/server/userFavorite";

// Dynamically import components
const NearbyPropertiesCarousel = dynamic(() => import("./NearbyPropertiesCarousel"), {
  loading: () => <div className="h-16 bg-white animate-pulse"></div>,
});

const PropertyImageGallery = dynamic(() => import("./PropertyImageGallery"), {
  loading: () => <div className="h-64 bg-gray-100 animate-pulse rounded-md"></div>,
});

const PropertyDescription = dynamic(() => import("./PropertyDescription"), {
  loading: () => <div className="h-64 bg-white animate-pulse"></div>,
});

const PropertyContactForm = dynamic(() => import("./PropertyContactForm"), {
  ssr: false,
});

const DetailMap = dynamic(() => import("./DetailMap"), {
  ssr: false,
  loading: () => <div className="h-64 bg-gray-100 animate-pulse rounded-md"></div>,
});

export default function PropertyDetailModal({ property, onClose, isFavorite = false, onToggleFavorite }) {
  const [isOpen, setIsOpen] = useState(true);
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);
  const [favorite, setFavorite] = useState(isFavorite);
  const [isLoading, setIsLoading] = useState(false);
  const tCommon = useTranslations("Common");
  const t = useTranslations("PropertyCard");
  const { toast } = useToast();
  const { isLoggedIn } = useAuth();

  // Update local favorite state when prop changes
  useEffect(() => {
    setFavorite(isFavorite);
  }, [isFavorite]);

  const handleFavoriteClick = async (e) => {
    e.stopPropagation();

    if (!isLoggedIn) {
      if (onToggleFavorite) {
        onToggleFavorite(property.id, false);
      }
      return;
    }

    setIsLoading(true);
    try {
      const newFavoriteStatus = !favorite;
      const result = newFavoriteStatus
        ? await addToFavorites(property.id)
        : await removeFromFavorites(property.id);

      if (result.success) {
        setFavorite(newFavoriteStatus);
        if (onToggleFavorite) {
          onToggleFavorite(property.id, newFavoriteStatus);
        }
        toast({
          title: newFavoriteStatus ? t("addedToFavorites") : t("removedFromFavorites"),
          description: newFavoriteStatus
            ? t("addedToFavoritesDesc")
            : t("removedFromFavoritesDesc"),
          variant: "default",
        });
      } else {
        toast({
          title: t("errorOccurred"),
          description: result.message || t("cannotUpdateFavorite"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      toast({
        title: t("errorOccurred"),
        description: t("cannotUpdateFavorite"),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    if (onClose) onClose();
  };

  const handleContactModalClose = () => {
    setIsContactModalOpen(false);
  };

  let formattedAddress = property.address || "";
  let center = { latitude: property.latitude, longitude: property.longitude };

  try {
    if (property.placeData) {
      const placeData = JSON.parse(property.placeData);
      if (placeData.result && placeData.result.formatted_address) {
        formattedAddress = placeData.result.formatted_address;
      }
    }
  } catch (e) {
    console.error("Error parsing placeData:", e);
  }

  const content = (
    <div className="bg-white mx-3">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-white p-4 border-b flex items-center justify-between">
        <button
          onClick={onClose ? handleClose : () => window.history.back()}
          className="flex items-center text-gray-600"
        >
          <ChevronLeft className="h-5 w-5 mr-2" />
          <span>Quay lại</span>
        </button>
        <div className="flex-1 flex justify-center">
          <Image src="/yezhome_logo.png" alt="YEZ Home" width={120} height={120} />
        </div>
        <div className="flex items-center gap-3">
          <button
            className={`flex items-center gap-1 ${favorite ? 'text-coral-500' : 'text-gray-700'} transition-colors duration-300`}
            onClick={handleFavoriteClick}
            disabled={isLoading}
          >
            <Heart className={`h-5 w-5 ${isLoading ? 'animate-pulse' : ''}`} fill={favorite ? "currentColor" : "none"} />
            <span className="hidden sm:inline">Lưu</span>
          </button>
          <button className="flex items-center gap-1 text-gray-700">
            <Share className="h-5 w-5" />
            <span className="hidden sm:inline">Chia sẻ</span>
          </button>
          <button
            onClick={onClose ? handleClose : () => window.history.back()}
            className="flex items-center gap-1 text-gray-700"
          >
            <X className="h-5 w-5" />
            <span className="hidden sm:inline">Đóng</span>
          </button>
        </div>
      </header>

      {/* Property Images */}
      <div className="p-4">
        <PropertyImageGallery
          images={property.propertyMedia?.map((pm) => pm.mediaURL) || []}
          propertyName={property.name}
        />
      </div>

      {/* Property Details */}
      <div className="p-4 grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <PropertyDescription
            property={{
              ...property,
              address: formattedAddress,
              propertyType: property.propertyType
                ? tCommon(`propertyType_${property.propertyType}`)
                : "",
              postType: property.postType ? tCommon(`propertyPostType_${property.postType}`) : "",
              direction: property.direction ? tCommon(`${property.direction}`) : "__",
              balconyDirection: property.balconyDirection
                ? tCommon(`${property.balconyDirection}`)
                : "__",
              legality: property.legality ? tCommon(`legality_${property.legality}`) : "__",
              interior: property.interior ? tCommon(`interior_${property.interior}`) : "__",
            }}
          />
          {/* Map */}
          <div className="p-4">
            <h2 className="text-2xl font-bold mb-4">Vị trí bất động sản</h2>
            <DetailMap property={property} center={center} />
          </div>

          {/* Nearby Properties Section */}
          <div className="p-4">
            <div className="border-t pt-6">
              <h2 className="text-2xl font-bold mb-4">Bất động sản lân cận</h2>
              <NearbyPropertiesCarousel
                latitude={property.latitude}
                longitude={property.longitude}
                tCommon={tCommon}
              />
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="lg:col-span-1">
          <div className="bg-white border rounded-md p-4 sticky top-20">
            <Button
              className="w-full bg-teal-600 text-white hover:bg-teal-700 font-semibold py-7 px-4 rounded-md mb-3 text-lg"
              onClick={() => setIsContactModalOpen(true)}
            >
              <Send /> Liên hệ người bán
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  // If this is being used as a page view (no onClose provided), render without Dialog
  if (!onClose) {
    return content;
  }

  // Modal view with Dialog
  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent
          className="max-w-7xl p-0 h-[95vh] w-[90vw] [&>button:last-child]:hidden"
          onPointerDownOutside={(e) => e.preventDefault()}
        >
          <DialogTitle className="sr-only">{property.name || "Property Details"}</DialogTitle>
          <ScrollArea className="h-[94vh]">{content}</ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Contact Form Modal */}
      <PropertyContactForm
        isOpen={isContactModalOpen}
        onClose={handleContactModalClose}
        propertyId={property.id}
        ownerId={property.ownerId}
      />
    </>
  );
}

{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_8399fde0._.js", "server/edge/chunks/[root-of-the-server]__49146aab._.js", "server/edge/chunks/edge-wrapper_041f7dec.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ibuBR8gd9czNjVKcVb89mS9lA91pSiY5k7/21W4re7I=", "__NEXT_PREVIEW_MODE_ID": "eed88655f0903b8eb77aab86f242142c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f93d81569c77f5f8cfcebaf2910a04882cb14608f20e30da7246986d08e03878", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "24750fd140122305b590c10363caad2ffac9151be733e8bec2cf03c6dd9ca755"}}}, "instrumentation": null, "functions": {}}
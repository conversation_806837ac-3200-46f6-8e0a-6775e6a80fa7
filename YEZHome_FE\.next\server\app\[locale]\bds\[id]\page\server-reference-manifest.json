{"node": {"001de94ec731220815d4fe6ce2d548b202bd052ff3": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "0034f2076260b358ea3dfc1c99fa419e3287163fe8": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "007be0fe05adcc7d0c069ca539eb0ff1cb6fd0e443": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}, "00e7e2892a7c5df6d1d5fa13dd13a750332361b7bf": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}, "605ee68581d93fd51fe0565806b8059b6a037fc225": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "6074658acb00601d2549775ad0d80ebfad3207beb6": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "6095e1a16a36fae9f991406ee5d3ae93ce05419f13": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "60ddee3a1e9f4d6efc9c1cece9c322d5fbc2422f7f": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "008dfdacd08dee8b2631add445c74492baff98a2ad": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "00e6dbd535aa6e9ad1aa89e2cac9bd8d5cc801465a": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "4001fad38119db8542322dccd0617b3df1d830a26c": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "403e60a2cf4748152b9343ec01a868c4669796cd15": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "4096ae64ac4ea3209d6dc5820144fc5deef2f95a15": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "60a89ef542525d5dfde77653987c6ed3b387c5216e": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "60f988a13a61f71753d0e8e0e1219596262b22d654": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "70c1d52c2370d1547b5942fa95004975d259c404e8": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "004337d8d5eb6ed7e2919a0aeefac685ea3d2d1941": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}, "40208af54e01b051461b63d477eaaaa55f04d9b278": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}, "00bbe381627ea72a4cce4f9c30bb837f34cc1bd027": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}, "40ae052a3c4bb7eb61dbfdc1c4f786ce752c93bed7": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}, "00119bbc209304daaae56f240d2039a6a43dcb0320": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "00988f8ed4a3cfa99fb8476b3194891831467e44d7": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "401a97280d5ce9565943fdcbe639ca98ed3df6816d": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "4028a95d00369331b3bf78498e966fc69039c396d5": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "40349d8aaa75adc55fddf542d24addefd0ff4b906e": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "409ad784b92a1a313639c04f81ca2c94d075b8cf47": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "40d4df3ad783630ce7b196706828eecdfd7c2d2e76": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "40dc8e5d0cd6942972a005ff59fb5313d50c976a55": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "40e516e88dde32b6d807dd4efdd3a65accb46d6fe9": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "40f96f59a10ba7f88d0643fabe18bd637d34fed74a": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "40fccd474b4ea91169f388ffb2b5596f86061fa12c": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "60132b3bf5fb49b2ec0328a6e8371a7e0f39f45431": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "601a34545aea8e8cc50a9d61816b2f06952d565a6a": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "602db38a384954084ad869270e409d791ecc061b65": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "6052a8d78b815c6399a4eeca17f65535c07e14995d": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "605e9ce34b71779c72cc0fd79fadaa516765e289db": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "60784b5203110f1c09e12e8495bffd4450889b78b9": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "60eb601005c54d544b3062e77d4c5ae627e94fd88b": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "701b1d6cac263a13e24049630543eb96880c1a9529": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "7085800a14a02e3d0acf1cb90a916071c6624bb6c0": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "rsc"}}, "70b1560c1d490fc56c914b2936e74a400a310408cd": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}, "40b8bf52df741401c7c2f61d096cc243772a94f063": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}, "40f2b1915562886d43a901d95ff8c50055a5956498": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}, "403c24c3bd50d955eb88cdadad68d9b1fe0078c414": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}, "60807ffa3e0d325e4e19a925a0c7e573b08aace120": {"workers": {"app/[locale]/bds/[id]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/bds/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/bds/[id]/page": "action-browser"}}}, "edge": {}}
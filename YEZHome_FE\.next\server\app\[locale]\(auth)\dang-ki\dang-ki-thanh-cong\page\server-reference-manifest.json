{"node": {"001de94ec731220815d4fe6ce2d548b202bd052ff3": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "0034f2076260b358ea3dfc1c99fa419e3287163fe8": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "007be0fe05adcc7d0c069ca539eb0ff1cb6fd0e443": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "action-browser"}}, "00e7e2892a7c5df6d1d5fa13dd13a750332361b7bf": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "action-browser"}}, "605ee68581d93fd51fe0565806b8059b6a037fc225": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "6074658acb00601d2549775ad0d80ebfad3207beb6": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "6095e1a16a36fae9f991406ee5d3ae93ce05419f13": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "60ddee3a1e9f4d6efc9c1cece9c322d5fbc2422f7f": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "008dfdacd08dee8b2631add445c74492baff98a2ad": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "00e6dbd535aa6e9ad1aa89e2cac9bd8d5cc801465a": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "4001fad38119db8542322dccd0617b3df1d830a26c": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "403e60a2cf4748152b9343ec01a868c4669796cd15": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "4096ae64ac4ea3209d6dc5820144fc5deef2f95a15": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "60a89ef542525d5dfde77653987c6ed3b387c5216e": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "60f988a13a61f71753d0e8e0e1219596262b22d654": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "70c1d52c2370d1547b5942fa95004975d259c404e8": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "rsc"}}, "004337d8d5eb6ed7e2919a0aeefac685ea3d2d1941": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "action-browser"}}, "40208af54e01b051461b63d477eaaaa55f04d9b278": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "action-browser"}}, "00bbe381627ea72a4cce4f9c30bb837f34cc1bd027": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "action-browser"}}, "40ae052a3c4bb7eb61dbfdc1c4f786ce752c93bed7": {"workers": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page": "action-browser"}}}, "edge": {}}
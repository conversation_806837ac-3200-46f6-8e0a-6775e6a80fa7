const CHUNK_PUBLIC_PATH = "server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page.js";
const runtime = require("../../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_c336f25b._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/app_1f3630ef._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/messages_6b97b043._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__f6458b17._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_0eb5a6bb._.js");
runtime.loadChunk("server/chunks/ssr/_9d1df110._.js");
runtime.loadChunk("server/chunks/ssr/app_[locale]_not-found_jsx_7aca902c._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_735cdf99._.js");
runtime.loadChunk("server/chunks/ssr/_53082ec8._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/[locale]/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/[locale]/loading.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/[locale]/not-found.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page.jsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/[locale]/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/[locale]/loading.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/[locale]/not-found.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/[locale]/(auth)/dang-ki/dang-ki-thanh-cong/page.jsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// New function to get property report data (Placeholder)\r\nexport async function getPropertyReportById(propertyId) {\r\n  \"use server\"; // Ensure this runs on the server\r\n\r\n  // Simulate API call delay\r\n  await new Promise(resolve => setTimeout(resolve, 750));\r\n\r\n  try {\r\n    // --- FAKE DATA ---\r\n    // In a real scenario, you would fetch this from your API:\r\n    // const response = await fetchWithAuth(`${API_BASE_URL}/report/${propertyId}`);\r\n    // if (!response.success) return response;\r\n    // const reportData = response.data;\r\n\r\n    // For now, generate some fake data:\r\n    const fakeReportData = {\r\n      views: 30 + Math.floor(Math.random() * 100),           // Lượt xem\r\n      impressions: 33 + Math.floor(Math.random() * 150),    // Lượt hiển thị\r\n      cartAdds: 5 + Math.floor(Math.random() * 40),         // Lượt bỏ vào giỏ hàng (Example)\r\n      contactRequests: 10 + Math.floor(Math.random() * 20), // Lượt liên hệ (Example - added based on previous comment)\r\n      highlightsCount: 1 + Math.floor(Math.random() * 15),  // Số lần highlight\r\n      renewalsCount: 0 + Math.floor(Math.random() * 5),     // Số lần gia hạn\r\n      postCost: 55000,                                      // Tiền bài đăng\r\n      highlightCost: (1 + Math.floor(Math.random() * 15)) * 40000, // Tổng tiền highlight\r\n      renewalCost: (0 + Math.floor(Math.random() * 5)) * 300000, // Tổng tiền gia hạn\r\n    };\r\n    fakeReportData.totalCost = fakeReportData.postCost + fakeReportData.highlightCost + fakeReportData.renewalCost;\r\n\r\n    return {\r\n      success: true,\r\n      data: fakeReportData,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAUsB,mBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/userFavorite.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/UserFavorites`;\r\n\r\nexport async function addToFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/add`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyId }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"addToFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi thêm vào danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function removeFromFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/remove/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"removeFromFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa khỏi danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function checkFavoriteStatus(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/check`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyIds: Array.isArray(propertyIds) ? propertyIds : [propertyIds] }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkFavoriteStatus\",\r\n      propertyIds,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function getFavoritesCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getFavoritesCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số lượng bất động sản yêu thích\");\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the user's favorite properties\r\n * @returns {Promise<{success: boolean, data: Array<UserFavoriteDto>, message: string}>} Response with array of UserFavoriteDto objects\r\n * @description UserFavoriteDto contains: id, propertyId, createdAt\r\n */\r\nexport async function getUserFavorites() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/favorites`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserFavorites\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích\");\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the user's favorite properties with full property details, pagination and filtering\r\n * @param {Object} filters - Filter options\r\n * @param {number} filters.minPrice - Minimum price filter\r\n * @param {number} filters.maxPrice - Maximum price filter\r\n * @param {string} filters.fromDate - Start date filter (ISO string)\r\n * @param {string} filters.toDate - End date filter (ISO string)\r\n * @param {string} filters.sortBy - Sort field (CreatedAt, Price)\r\n * @param {boolean} filters.sortDescending - Sort direction\r\n * @param {number} filters.page - Page number\r\n * @param {number} filters.pageSize - Items per page\r\n * @returns {Promise<{success: boolean, data: PagedFavoriteResultDto, message: string}>} Response with paginated favorites and property details\r\n */\r\nexport async function getUserFavoritesWithDetails(filters = {}) {\r\n  try {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    if (filters.minPrice !== undefined && filters.minPrice !== null) {\r\n      queryParams.append('minPrice', filters.minPrice.toString());\r\n    }\r\n    if (filters.maxPrice !== undefined && filters.maxPrice !== null) {\r\n      queryParams.append('maxPrice', filters.maxPrice.toString());\r\n    }\r\n    if (filters.fromDate) {\r\n      queryParams.append('fromDate', filters.fromDate);\r\n    }\r\n    if (filters.toDate) {\r\n      queryParams.append('toDate', filters.toDate);\r\n    }\r\n    if (filters.sortBy) {\r\n      queryParams.append('sortBy', filters.sortBy);\r\n    }\r\n    if (filters.sortDescending !== undefined) {\r\n      queryParams.append('sortDescending', filters.sortDescending.toString());\r\n    }\r\n    if (filters.page) {\r\n      queryParams.append('page', filters.page.toString());\r\n    }\r\n    if (filters.pageSize) {\r\n      queryParams.append('pageSize', filters.pageSize.toString());\r\n    }\r\n\r\n    const url = `${API_BASE_URL}/favorites-with-details${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n\r\n    return await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserFavoritesWithDetails\",\r\n      filters,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0CsB,sBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/page.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback, useMemo } from \"react\";\r\nimport { useRouter, useSearchParams, usePathname } from \"next/navigation\";\r\nimport dynamic from \"next/dynamic\";\r\nimport { searchProperties } from \"@/app/actions/server/property\";\r\nimport { checkFavoriteStatus } from \"@/app/actions/server/userFavorite\";\r\nimport { toast } from \"@/hooks/use-toast\";\r\nimport { HCM_COORDINATES_DISTRICT_2 } from \"@/lib/enum\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nconst PropertyDetailModal = dynamic(() => import(\"@/components/property/PropertyDetailModal\"), {\r\n  ssr: false,\r\n  loading: () => <div className=\"h-16 bg-white animate-pulse\"></div>,\r\n});\r\n// Dynamically import heavy components\r\nconst SearchFilterComponent = dynamic(() => import(\"@/components/property/SearchFilter\"), {\r\n  loading: () => <div className=\"h-16 bg-white animate-pulse\"></div>,\r\n});\r\n\r\nconst MapSectionComponent = dynamic(() => import(\"@/components/property/HomeMap\"), {\r\n  ssr: false, // Map libraries often need to be client-side only\r\n  loading: () => <div className=\"flex-1 md:w-3/5 bg-gray-100 animate-pulse\"></div>,\r\n});\r\n\r\nconst PropertyListComponent = dynamic(() => import(\"@/components/property/PropertyList\"), {\r\n  loading: () => <div className=\"w-full md:w-2/5 bg-white animate-pulse\"></div>,\r\n});\r\n\r\n// Helper function to parse URL parameters into filter criteria\r\nconst parseUrlToFilterCriteria = (searchParams) => {\r\n  const filterCriteria = {\r\n    transactionType: [],\r\n    propertyType: [],\r\n    location: {\r\n      province: \"\",\r\n      district: \"\",\r\n      address: \"\",\r\n    },\r\n    priceRange: {\r\n      min: \"\",\r\n      max: \"\",\r\n    },\r\n    areaRange: {\r\n      min: \"\",\r\n      max: \"\",\r\n    },\r\n    bedrooms: \"\",\r\n    bathrooms: \"\",\r\n    direction: \"\",\r\n    legalStatus: \"\",\r\n    roadWidth: \"\",\r\n  };\r\n\r\n  // Parse transaction type\r\n  const postType = searchParams.getAll(\"postType\");\r\n  if (postType.length > 0) {\r\n    filterCriteria.transactionType = postType;\r\n  }\r\n\r\n  // Parse property type\r\n  const propertyType = searchParams.getAll(\"propertyType\");\r\n  if (propertyType.length > 0) {\r\n    filterCriteria.propertyType = propertyType;\r\n  }\r\n\r\n  // Parse location\r\n  const province = searchParams.get(\"cityId\");\r\n  if (province) filterCriteria.location.province = province;\r\n\r\n  const district = searchParams.get(\"districtId\");\r\n  if (district) filterCriteria.location.district = district;\r\n\r\n  const address = searchParams.get(\"address\");\r\n  if (address) filterCriteria.location.address = address;\r\n\r\n  // Parse price range\r\n  const minPrice = searchParams.get(\"minPrice\");\r\n  if (minPrice) filterCriteria.priceRange.min = minPrice;\r\n\r\n  const maxPrice = searchParams.get(\"maxPrice\");\r\n  if (maxPrice) filterCriteria.priceRange.max = maxPrice;\r\n\r\n  // Parse area range\r\n  const minArea = searchParams.get(\"minArea\");\r\n  if (minArea) filterCriteria.areaRange.min = minArea;\r\n\r\n  const maxArea = searchParams.get(\"maxArea\");\r\n  if (maxArea) filterCriteria.areaRange.max = maxArea;\r\n\r\n  // Parse other filters\r\n  const bedrooms = searchParams.get(\"minRooms\");\r\n  if (bedrooms) filterCriteria.bedrooms = bedrooms;\r\n\r\n  const bathrooms = searchParams.get(\"minToilets\");\r\n  if (bathrooms) filterCriteria.bathrooms = bathrooms;\r\n\r\n  const direction = searchParams.get(\"direction\");\r\n  if (direction) filterCriteria.direction = direction;\r\n\r\n  const legalStatus = searchParams.get(\"legality\");\r\n  if (legalStatus) filterCriteria.legalStatus = legalStatus;\r\n\r\n  const roadWidth = searchParams.get(\"minRoadWidth\");\r\n  if (roadWidth) filterCriteria.roadWidth = roadWidth;\r\n\r\n  return filterCriteria;\r\n};\r\n\r\n// Helper function to convert filter criteria to URL parameters\r\nconst filterCriteriaToUrlParams = (criteria) => {\r\n  const params = new URLSearchParams();\r\n\r\n  // Transaction Type\r\n  if (criteria.transactionType && criteria.transactionType.length > 0) {\r\n    criteria.transactionType.forEach((type) => {\r\n      params.append(\"postType\", type);\r\n    });\r\n  }\r\n\r\n  // Property Type\r\n  if (criteria.propertyType && criteria.propertyType.length > 0) {\r\n    criteria.propertyType.forEach((type) => {\r\n      params.append(\"propertyType\", type);\r\n    });\r\n  }\r\n\r\n  // Location\r\n  if (criteria.location) {\r\n    if (criteria.location.province) {\r\n      params.append(\"cityId\", criteria.location.province);\r\n    }\r\n    if (criteria.location.district) {\r\n      params.append(\"districtId\", criteria.location.district);\r\n    }\r\n    if (criteria.location.address) {\r\n      params.append(\"address\", criteria.location.address);\r\n    }\r\n  }\r\n\r\n  // Price Range\r\n  if (criteria.priceRange) {\r\n    if (criteria.priceRange.min) {\r\n      params.append(\"minPrice\", criteria.priceRange.min);\r\n    }\r\n    if (criteria.priceRange.max) {\r\n      params.append(\"maxPrice\", criteria.priceRange.max);\r\n    }\r\n  }\r\n\r\n  // Area Range\r\n  if (criteria.areaRange) {\r\n    if (criteria.areaRange.min) {\r\n      params.append(\"minArea\", criteria.areaRange.min);\r\n    }\r\n    if (criteria.areaRange.max) {\r\n      params.append(\"maxArea\", criteria.areaRange.max);\r\n    }\r\n  }\r\n\r\n  // Other filters\r\n  if (criteria.bedrooms) {\r\n    params.append(\"minRooms\", criteria.bedrooms);\r\n  }\r\n  if (criteria.bathrooms) {\r\n    params.append(\"minToilets\", criteria.bathrooms);\r\n  }\r\n  if (criteria.direction) {\r\n    params.append(\"direction\", criteria.direction);\r\n  }\r\n  if (criteria.legalStatus) {\r\n    params.append(\"legality\", criteria.legalStatus);\r\n  }\r\n  if (criteria.roadWidth) {\r\n    params.append(\"minRoadWidth\", criteria.roadWidth);\r\n  }\r\n\r\n  return params.toString();\r\n};\r\n\r\nexport default function Home() {\r\n  const tError = useTranslations(\"ErrorMessage\");\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const pathname = usePathname();\r\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\r\n  const { isLoggedIn } = useAuth();\r\n\r\n  const [filterCriteria, setFilterCriteria] = useState({\r\n    transactionType: [],\r\n    propertyType: [],\r\n    location: {\r\n      province: \"\",\r\n      district: \"\",\r\n      address: \"\",\r\n    },\r\n    priceRange: {\r\n      min: \"\",\r\n      max: \"\",\r\n    },\r\n    areaRange: {\r\n      min: \"\",\r\n      max: \"\",\r\n    },\r\n    bedrooms: \"\",\r\n    bathrooms: \"\",\r\n    direction: \"\",\r\n    legalStatus: \"\",\r\n    roadWidth: \"\",\r\n    page: 1,\r\n    pageSize: 10,\r\n    sw_lat: null,\r\n    sw_lng: null,\r\n    ne_lat: null,\r\n    ne_lng: null,\r\n  });\r\n\r\n  const [filteredProperties, setFilteredProperties] = useState([]);\r\n  const [paginationInfo, setPaginationInfo] = useState({\r\n    totalCount: 0,\r\n    pageCount: 1,\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    hasNextPage: false,\r\n    hasPreviousPage: false,\r\n  });\r\n  const [selectedProperty, setSelectedProperty] = useState(null);\r\n  const [selectedPropertyForModal, setSelectedPropertyForModal] = useState(null);\r\n  const [favorites, setFavorites] = useState({});\r\n\r\n  // --- State cho Vị trí ---\r\n  const [userLocation, setUserLocation] = useState(null);\r\n  const [locationError, setLocationError] = useState(null);\r\n  const [isLoadingLocation, setIsLoadingLocation] = useState(true);\r\n  const [finalLocation, setFinalLocation] = useState(null);\r\n\r\n  // --- State cho Danh sách Bất động sản ---\r\n  const [properties, setProperties] = useState(null);\r\n  const [isLoadingProperties, setIsLoadingProperties] = useState(false);\r\n  const [propertiesError, setPropertiesError] = useState(null);\r\n\r\n  const [mapBounds, setMapBounds] = useState(null);\r\n\r\n  // Get user's location on component mount - modified to not set default location when permission denied\r\n  useEffect(() => {\r\n\r\n    // Kiểm tra hỗ trợ Geolocation API\r\n    if (!navigator.geolocation) {\r\n      setLocationError(new Error(\"Trình duyệt của bạn không hỗ trợ Geolocation.\"));\r\n      setIsLoadingLocation(false);\r\n\r\n      // Xác định và set finalLocation ngay cả khi có lỗi hoặc không hỗ trợ\r\n      setFinalLocation(HCM_COORDINATES_DISTRICT_2); // Set vị trí mặc định\r\n\r\n      return;\r\n    }\r\n\r\n    // Callback khi lấy vị trí thành công\r\n    const successHandler = (position) => {\r\n\r\n      const determinedLocation = {\r\n        latitude: position.coords.latitude,\r\n        longitude: position.coords.longitude,\r\n        accuracy: position.coords.accuracy,\r\n      };\r\n\r\n      setUserLocation(determinedLocation);\r\n      setLocationError(null); // Xóa lỗi nếu có\r\n      setIsLoadingLocation(false);\r\n      // Set finalLocation khi lấy vị trí thành công\r\n      setFinalLocation(determinedLocation);\r\n    };\r\n\r\n    // Callback khi lấy vị trí thất bại\r\n    const errorHandler = (err) => {\r\n      setLocationError(new Error(`Lỗi ${err.code}: ${err.message}`));\r\n      setUserLocation(null); // Đảm bảo userLocation là null khi có lỗi\r\n      setIsLoadingLocation(false);\r\n      // Set finalLocation khi lấy vị trí thất bại\r\n      setFinalLocation(HCM_COORDINATES_DISTRICT_2); // Set vị trí mặc định\r\n\r\n      // Hiển thị toast thông báo lỗi cho người dùng\r\n      toast({\r\n        title: tError(\"locationErrorTitle\"),\r\n        description: tError(\"locationErrorDescription\", { message: err.message }),\r\n        className: \"bg-red-600 text-white\",\r\n      });\r\n    };\r\n\r\n    // Tùy chọn cấu hình cho getCurrentPosition\r\n    const options = {\r\n      enableHighAccuracy: true, // Ưu tiên độ chính xác\r\n      timeout: 10000, // Thời gian chờ tối đa (ms)\r\n      maximumAge: 0, // Không dùng cache vị trí cũ\r\n    };\r\n\r\n    // Gọi API lấy vị trí hiện tại\r\n    // Đây là nơi trình duyệt sẽ hiển thị popup xin quyền người dùng\r\n    navigator.geolocation.getCurrentPosition(successHandler, errorHandler, options);\r\n  }, []);\r\n\r\n  // Parse URL parameters on initial load\r\n  useEffect(() => {\r\n    if (searchParams && isInitialLoad) {\r\n      const urlFilterCriteria = parseUrlToFilterCriteria(searchParams);\r\n      setFilterCriteria(urlFilterCriteria);\r\n      setIsInitialLoad(false);\r\n    }\r\n  }, [searchParams, isInitialLoad]);\r\n\r\n  // Fetch properties based on filter criteria\r\n  useEffect(() => {   \r\n    // Chỉ tiến hành fetch nếu finalLocation đã được set\r\n    // và chưa có dữ liệu BĐS (tránh fetch lại không cần thiết khi component render lại)\r\n    // và không đang trong quá trình fetch\r\n    if (!mapBounds || isLoadingProperties) {\r\n      return; // Dừng nếu chưa sẵn sàng hoặc đã fetch\r\n    }\r\n\r\n    // Bắt đầu quá trình fetch BĐS\r\n    setIsLoadingProperties(true);\r\n    setPropertiesError(null); // Xóa lỗi fetch trước đó nếu có\r\n\r\n    const fetchProperties = async () => {\r\n      try {\r\n        // Add user location to filter criteria for proximity search ONLY if available\r\n        const searchCriteria = {\r\n          ...filterCriteria,\r\n        };\r\n\r\n        const response = await searchProperties(searchCriteria);\r\n\r\n        if (response.success) {\r\n          // Update state with paginated data\r\n          setPropertiesError(null); // Xóa lỗi fetch\r\n          setProperties(response.data.items);\r\n          setFilteredProperties(response.data.items);\r\n          setPaginationInfo({\r\n            totalCount: response.data.totalCount,\r\n            pageCount: response.data.pageCount,\r\n            currentPage: response.data.currentPage,\r\n            pageSize: response.data.pageSize,\r\n            hasNextPage: response.data.hasNextPage,\r\n            hasPreviousPage: response.data.hasPreviousPage,\r\n          });\r\n        } else {\r\n          setProperties(null); // Xóa data BĐS cũ khi có lỗi\r\n          setPropertiesError(response.message); // Cập nhật state lỗi fetch\r\n          toast({\r\n            title: \"Lỗi\",\r\n            description: response.message,\r\n            variant: \"destructive\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        setProperties(null); // Xóa data BĐS cũ khi có lỗi\r\n        setPropertiesError(error); // Cập nhật state lỗi fetch\r\n        toast({\r\n          title: \"Lỗi\",\r\n          description: \"Đã xảy ra lỗi khi tải dữ liệu\",\r\n          variant: \"destructive\",\r\n        });\r\n      } finally {\r\n        setIsLoadingProperties(false);\r\n      }\r\n    };\r\n\r\n    fetchProperties();\r\n  }, [filterCriteria, finalLocation, mapBounds]);\r\n\r\n  // Check favorite status when properties change and user is logged in\r\n  useEffect(() => {\r\n    const fetchFavoriteStatus = async () => {\r\n      if (!isLoggedIn || !filteredProperties || filteredProperties.length === 0) return;\r\n\r\n      try {\r\n        const propertyIds = filteredProperties.map((p) => p.id);\r\n        const result = await checkFavoriteStatus(propertyIds);\r\n\r\n        if (result.success) {\r\n          const favoriteMap = {};\r\n          result.data.forEach((item) => {\r\n            favoriteMap[item.propertyId] = item.isFavorite;\r\n          });\r\n          setFavorites(favoriteMap);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching favorite status:\", error);\r\n      }\r\n    };\r\n\r\n    if (isLoggedIn) {\r\n      fetchFavoriteStatus();\r\n    }\r\n  }, [filteredProperties, isLoggedIn]);\r\n\r\n  // Tạo mảng markers từ dữ liệu properties đã fetch\r\n  const propertyMarkers = useMemo(() => {\r\n    if (!properties) return [];\r\n    return properties\r\n      .map((prop) => {\r\n        if (prop.latitude !== undefined && prop.longitude !== undefined) {\r\n          return {\r\n            ...prop,\r\n            imageUrl: prop.propertyMedia?.[0]?.mediaURL,\r\n          };\r\n        }\r\n        return null;\r\n      })\r\n      .filter((marker) => marker !== null);\r\n  }, [properties]);\r\n\r\n  // Update URL when filter criteria changes\r\n  useEffect(() => {\r\n    if (isInitialLoad) return;\r\n\r\n    const urlParams = filterCriteriaToUrlParams(filterCriteria);\r\n    const url = urlParams ? `${pathname}?${urlParams}` : pathname;\r\n\r\n    // Update URL without refreshing the page\r\n    router.push(url, { scroll: false });\r\n  }, [filterCriteria, router, pathname, isInitialLoad]);\r\n\r\n  // Handle filter changes\r\n  const handleFilterChange = useCallback((newFilterCriteria) => {\r\n    setFilterCriteria(newFilterCriteria);\r\n  }, []);\r\n\r\n  // Handle property selection (for map centering and modal)\r\n  const handlePropertySelect = useCallback((property) => {\r\n    setSelectedProperty(property);\r\n    setSelectedPropertyForModal(property);\r\n  }, []);\r\n\r\n  // Handle page change\r\n  const handlePageChange = useCallback((page) => {\r\n    setFilterCriteria((prev) => ({\r\n      ...prev,\r\n      page,\r\n    }));\r\n  }, []);\r\n\r\n  const handleBoundsChange = useCallback((bounds) => {\r\n    setMapBounds(bounds);\r\n    setFilterCriteria((prev) => ({\r\n      ...prev,\r\n      sw_lat: bounds.getSouthWest().lat,\r\n      sw_lng: bounds.getSouthWest().lng,\r\n      ne_lat: bounds.getNorthEast().lat,\r\n      ne_lng: bounds.getNorthEast().lng,\r\n    }));\r\n  }, []);\r\n\r\n  // Handle toggling favorite status\r\n  const handleToggleFavorite = useCallback((propertyId, isFavorite) => {\r\n    setFavorites((prev) => ({\r\n      ...prev,\r\n      [propertyId]: isFavorite,\r\n    }));\r\n\r\n    // Dispatch a custom event that the navbar can listen to\r\n    window.dispatchEvent(new CustomEvent(\"favorites-changed\"));\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <main className=\"flex flex-col\">\r\n        <SearchFilterComponent\r\n          onFilterChange={handleFilterChange}\r\n          initialFilters={filterCriteria}\r\n        />\r\n        <div className=\"flex-grow flex flex-col md:flex-row\">\r\n          {/* Map Section */}\r\n          {/* Hiển thị Bản đồ - Chỉ render khi finalLocation đã được set */}\r\n          {finalLocation ? (\r\n            <MapSectionComponent\r\n              center={finalLocation}\r\n              onBoundsChange={handleBoundsChange}\r\n              onViewDetails={handlePropertySelect}\r\n              markers={propertyMarkers}\r\n              activePropertyId ={selectedProperty?.id}\r\n            ></MapSectionComponent>\r\n          ) : (\r\n            // Hiển thị thông báo khi chưa có finalLocation (chưa xác định vị trí)\r\n            !isLoadingLocation && <p>Thiếu API Key Goong Maps.</p> // Nếu không loading location và thiếu key\r\n            // Trường hợp đang loading location, thông báo \"Đang lấy vị trí...\" đã hiển thị bên trên\r\n          )}\r\n\r\n          {/* Hiển thị trạng thái lấy vị trí */}\r\n          {isLoadingLocation && (\r\n            <div className=\"w-full h-[calc(100vh-227px)] relative z-0 opacity-75 cursor-not-allowed\">\r\n              <div className=\"flex items-center justify-center h-full w-full\">Đang lấy vị trí hiện tại...</div>\r\n            </div>\r\n          )}\r\n          {/* Property List Section */}\r\n          <PropertyListComponent\r\n            properties={filteredProperties}\r\n            loading={isLoadingProperties}\r\n            onPropertySelect={handlePropertySelect}\r\n            pagination={paginationInfo}\r\n            onPageChange={handlePageChange}\r\n          />\r\n        </div>\r\n      </main>\r\n      {selectedPropertyForModal && (\r\n        <PropertyDetailModal\r\n          property={selectedPropertyForModal}\r\n          onClose={() => setSelectedPropertyForModal(null)}\r\n          isFavorite={!!favorites[selectedPropertyForModal.id]}\r\n          onToggleFavorite={handleToggleFavorite}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAVA;;;;;;;;;;;AAWA,MAAM,sBAAsB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAChC,KAAK;IACL,SAAS,kBAAM,8OAAC;YAAI,WAAU;;;;;;;AAEhC,sCAAsC;AACtC,MAAM,wBAAwB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACpC,SAAS,kBAAM,8OAAC;YAAI,WAAU;;;;;;;AAGhC,MAAM,sBAAsB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAChC,KAAK;IACL,SAAS,kBAAM,8OAAC;YAAI,WAAU;;;;;;;AAGhC,MAAM,wBAAwB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACpC,SAAS,kBAAM,8OAAC;YAAI,WAAU;;;;;;;AAGhC,+DAA+D;AAC/D,MAAM,2BAA2B,CAAC;IAChC,MAAM,iBAAiB;QACrB,iBAAiB,EAAE;QACnB,cAAc,EAAE;QAChB,UAAU;YACR,UAAU;YACV,UAAU;YACV,SAAS;QACX;QACA,YAAY;YACV,KAAK;YACL,KAAK;QACP;QACA,WAAW;YACT,KAAK;YACL,KAAK;QACP;QACA,UAAU;QACV,WAAW;QACX,WAAW;QACX,aAAa;QACb,WAAW;IACb;IAEA,yBAAyB;IACzB,MAAM,WAAW,aAAa,MAAM,CAAC;IACrC,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,eAAe,eAAe,GAAG;IACnC;IAEA,sBAAsB;IACtB,MAAM,eAAe,aAAa,MAAM,CAAC;IACzC,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,eAAe,YAAY,GAAG;IAChC;IAEA,iBAAiB;IACjB,MAAM,WAAW,aAAa,GAAG,CAAC;IAClC,IAAI,UAAU,eAAe,QAAQ,CAAC,QAAQ,GAAG;IAEjD,MAAM,WAAW,aAAa,GAAG,CAAC;IAClC,IAAI,UAAU,eAAe,QAAQ,CAAC,QAAQ,GAAG;IAEjD,MAAM,UAAU,aAAa,GAAG,CAAC;IACjC,IAAI,SAAS,eAAe,QAAQ,CAAC,OAAO,GAAG;IAE/C,oBAAoB;IACpB,MAAM,WAAW,aAAa,GAAG,CAAC;IAClC,IAAI,UAAU,eAAe,UAAU,CAAC,GAAG,GAAG;IAE9C,MAAM,WAAW,aAAa,GAAG,CAAC;IAClC,IAAI,UAAU,eAAe,UAAU,CAAC,GAAG,GAAG;IAE9C,mBAAmB;IACnB,MAAM,UAAU,aAAa,GAAG,CAAC;IACjC,IAAI,SAAS,eAAe,SAAS,CAAC,GAAG,GAAG;IAE5C,MAAM,UAAU,aAAa,GAAG,CAAC;IACjC,IAAI,SAAS,eAAe,SAAS,CAAC,GAAG,GAAG;IAE5C,sBAAsB;IACtB,MAAM,WAAW,aAAa,GAAG,CAAC;IAClC,IAAI,UAAU,eAAe,QAAQ,GAAG;IAExC,MAAM,YAAY,aAAa,GAAG,CAAC;IACnC,IAAI,WAAW,eAAe,SAAS,GAAG;IAE1C,MAAM,YAAY,aAAa,GAAG,CAAC;IACnC,IAAI,WAAW,eAAe,SAAS,GAAG;IAE1C,MAAM,cAAc,aAAa,GAAG,CAAC;IACrC,IAAI,aAAa,eAAe,WAAW,GAAG;IAE9C,MAAM,YAAY,aAAa,GAAG,CAAC;IACnC,IAAI,WAAW,eAAe,SAAS,GAAG;IAE1C,OAAO;AACT;AAEA,+DAA+D;AAC/D,MAAM,4BAA4B,CAAC;IACjC,MAAM,SAAS,IAAI;IAEnB,mBAAmB;IACnB,IAAI,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,MAAM,GAAG,GAAG;QACnE,SAAS,eAAe,CAAC,OAAO,CAAC,CAAC;YAChC,OAAO,MAAM,CAAC,YAAY;QAC5B;IACF;IAEA,gBAAgB;IAChB,IAAI,SAAS,YAAY,IAAI,SAAS,YAAY,CAAC,MAAM,GAAG,GAAG;QAC7D,SAAS,YAAY,CAAC,OAAO,CAAC,CAAC;YAC7B,OAAO,MAAM,CAAC,gBAAgB;QAChC;IACF;IAEA,WAAW;IACX,IAAI,SAAS,QAAQ,EAAE;QACrB,IAAI,SAAS,QAAQ,CAAC,QAAQ,EAAE;YAC9B,OAAO,MAAM,CAAC,UAAU,SAAS,QAAQ,CAAC,QAAQ;QACpD;QACA,IAAI,SAAS,QAAQ,CAAC,QAAQ,EAAE;YAC9B,OAAO,MAAM,CAAC,cAAc,SAAS,QAAQ,CAAC,QAAQ;QACxD;QACA,IAAI,SAAS,QAAQ,CAAC,OAAO,EAAE;YAC7B,OAAO,MAAM,CAAC,WAAW,SAAS,QAAQ,CAAC,OAAO;QACpD;IACF;IAEA,cAAc;IACd,IAAI,SAAS,UAAU,EAAE;QACvB,IAAI,SAAS,UAAU,CAAC,GAAG,EAAE;YAC3B,OAAO,MAAM,CAAC,YAAY,SAAS,UAAU,CAAC,GAAG;QACnD;QACA,IAAI,SAAS,UAAU,CAAC,GAAG,EAAE;YAC3B,OAAO,MAAM,CAAC,YAAY,SAAS,UAAU,CAAC,GAAG;QACnD;IACF;IAEA,aAAa;IACb,IAAI,SAAS,SAAS,EAAE;QACtB,IAAI,SAAS,SAAS,CAAC,GAAG,EAAE;YAC1B,OAAO,MAAM,CAAC,WAAW,SAAS,SAAS,CAAC,GAAG;QACjD;QACA,IAAI,SAAS,SAAS,CAAC,GAAG,EAAE;YAC1B,OAAO,MAAM,CAAC,WAAW,SAAS,SAAS,CAAC,GAAG;QACjD;IACF;IAEA,gBAAgB;IAChB,IAAI,SAAS,QAAQ,EAAE;QACrB,OAAO,MAAM,CAAC,YAAY,SAAS,QAAQ;IAC7C;IACA,IAAI,SAAS,SAAS,EAAE;QACtB,OAAO,MAAM,CAAC,cAAc,SAAS,SAAS;IAChD;IACA,IAAI,SAAS,SAAS,EAAE;QACtB,OAAO,MAAM,CAAC,aAAa,SAAS,SAAS;IAC/C;IACA,IAAI,SAAS,WAAW,EAAE;QACxB,OAAO,MAAM,CAAC,YAAY,SAAS,WAAW;IAChD;IACA,IAAI,SAAS,SAAS,EAAE;QACtB,OAAO,MAAM,CAAC,gBAAgB,SAAS,SAAS;IAClD;IAEA,OAAO,OAAO,QAAQ;AACxB;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAE7B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,iBAAiB,EAAE;QACnB,cAAc,EAAE;QAChB,UAAU;YACR,UAAU;YACV,UAAU;YACV,SAAS;QACX;QACA,YAAY;YACV,KAAK;YACL,KAAK;QACP;QACA,WAAW;YACT,KAAK;YACL,KAAK;QACP;QACA,UAAU;QACV,WAAW;QACX,WAAW;QACX,aAAa;QACb,WAAW;QACX,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,YAAY;QACZ,WAAW;QACX,aAAa;QACb,UAAU;QACV,aAAa;QACb,iBAAiB;IACnB;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAE5C,2BAA2B;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,2CAA2C;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,uGAAuG;IACvG,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QAER,kCAAkC;QAClC,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,iBAAiB,IAAI,MAAM;YAC3B,qBAAqB;YAErB,qEAAqE;YACrE,iBAAiB,2GAAA,CAAA,6BAA0B,GAAG,sBAAsB;YAEpE;QACF;QAEA,qCAAqC;QACrC,MAAM,iBAAiB,CAAC;YAEtB,MAAM,qBAAqB;gBACzB,UAAU,SAAS,MAAM,CAAC,QAAQ;gBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;gBACpC,UAAU,SAAS,MAAM,CAAC,QAAQ;YACpC;YAEA,gBAAgB;YAChB,iBAAiB,OAAO,iBAAiB;YACzC,qBAAqB;YACrB,8CAA8C;YAC9C,iBAAiB;QACnB;QAEA,mCAAmC;QACnC,MAAM,eAAe,CAAC;YACpB,iBAAiB,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE,IAAI,OAAO,EAAE;YAC5D,gBAAgB,OAAO,0CAA0C;YACjE,qBAAqB;YACrB,4CAA4C;YAC5C,iBAAiB,2GAAA,CAAA,6BAA0B,GAAG,sBAAsB;YAEpE,8CAA8C;YAC9C,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,OAAO,OAAO;gBACd,aAAa,OAAO,4BAA4B;oBAAE,SAAS,IAAI,OAAO;gBAAC;gBACvE,WAAW;YACb;QACF;QAEA,2CAA2C;QAC3C,MAAM,UAAU;YACd,oBAAoB;YACpB,SAAS;YACT,YAAY;QACd;QAEA,8BAA8B;QAC9B,gEAAgE;QAChE,UAAU,WAAW,CAAC,kBAAkB,CAAC,gBAAgB,cAAc;IACzE,GAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,eAAe;YACjC,MAAM,oBAAoB,yBAAyB;YACnD,kBAAkB;YAClB,iBAAiB;QACnB;IACF,GAAG;QAAC;QAAc;KAAc;IAEhC,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oDAAoD;QACpD,oFAAoF;QACpF,sCAAsC;QACtC,IAAI,CAAC,aAAa,qBAAqB;YACrC,QAAQ,uCAAuC;QACjD;QAEA,8BAA8B;QAC9B,uBAAuB;QACvB,mBAAmB,OAAO,gCAAgC;QAE1D,MAAM,kBAAkB;YACtB,IAAI;gBACF,8EAA8E;gBAC9E,MAAM,iBAAiB;oBACrB,GAAG,cAAc;gBACnB;gBAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gKAAA,CAAA,mBAAgB,AAAD,EAAE;gBAExC,IAAI,SAAS,OAAO,EAAE;oBACpB,mCAAmC;oBACnC,mBAAmB,OAAO,gBAAgB;oBAC1C,cAAc,SAAS,IAAI,CAAC,KAAK;oBACjC,sBAAsB,SAAS,IAAI,CAAC,KAAK;oBACzC,kBAAkB;wBAChB,YAAY,SAAS,IAAI,CAAC,UAAU;wBACpC,WAAW,SAAS,IAAI,CAAC,SAAS;wBAClC,aAAa,SAAS,IAAI,CAAC,WAAW;wBACtC,UAAU,SAAS,IAAI,CAAC,QAAQ;wBAChC,aAAa,SAAS,IAAI,CAAC,WAAW;wBACtC,iBAAiB,SAAS,IAAI,CAAC,eAAe;oBAChD;gBACF,OAAO;oBACL,cAAc,OAAO,6BAA6B;oBAClD,mBAAmB,SAAS,OAAO,GAAG,2BAA2B;oBACjE,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE;wBACJ,OAAO;wBACP,aAAa,SAAS,OAAO;wBAC7B,SAAS;oBACX;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,cAAc,OAAO,6BAA6B;gBAClD,mBAAmB,QAAQ,2BAA2B;gBACtD,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF,SAAU;gBACR,uBAAuB;YACzB;QACF;QAEA;IACF,GAAG;QAAC;QAAgB;QAAe;KAAU;IAE7C,qEAAqE;IACrE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,IAAI,CAAC,cAAc,CAAC,sBAAsB,mBAAmB,MAAM,KAAK,GAAG;YAE3E,IAAI;gBACF,MAAM,cAAc,mBAAmB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;gBACtD,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,sBAAmB,AAAD,EAAE;gBAEzC,IAAI,OAAO,OAAO,EAAE;oBAClB,MAAM,cAAc,CAAC;oBACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;wBACnB,WAAW,CAAC,KAAK,UAAU,CAAC,GAAG,KAAK,UAAU;oBAChD;oBACA,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD;QACF;QAEA,IAAI,YAAY;YACd;QACF;IACF,GAAG;QAAC;QAAoB;KAAW;IAEnC,kDAAkD;IAClD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,OAAO,EAAE;QAC1B,OAAO,WACJ,GAAG,CAAC,CAAC;YACJ,IAAI,KAAK,QAAQ,KAAK,aAAa,KAAK,SAAS,KAAK,WAAW;gBAC/D,OAAO;oBACL,GAAG,IAAI;oBACP,UAAU,KAAK,aAAa,EAAE,CAAC,EAAE,EAAE;gBACrC;YACF;YACA,OAAO;QACT,GACC,MAAM,CAAC,CAAC,SAAW,WAAW;IACnC,GAAG;QAAC;KAAW;IAEf,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe;QAEnB,MAAM,YAAY,0BAA0B;QAC5C,MAAM,MAAM,YAAY,GAAG,SAAS,CAAC,EAAE,WAAW,GAAG;QAErD,yCAAyC;QACzC,OAAO,IAAI,CAAC,KAAK;YAAE,QAAQ;QAAM;IACnC,GAAG;QAAC;QAAgB;QAAQ;QAAU;KAAc;IAEpD,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,kBAAkB;IACpB,GAAG,EAAE;IAEL,0DAA0D;IAC1D,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,oBAAoB;QACpB,4BAA4B;IAC9B,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,kBAAkB,CAAC,OAAS,CAAC;gBAC3B,GAAG,IAAI;gBACP;YACF,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,aAAa;QACb,kBAAkB,CAAC,OAAS,CAAC;gBAC3B,GAAG,IAAI;gBACP,QAAQ,OAAO,YAAY,GAAG,GAAG;gBACjC,QAAQ,OAAO,YAAY,GAAG,GAAG;gBACjC,QAAQ,OAAO,YAAY,GAAG,GAAG;gBACjC,QAAQ,OAAO,YAAY,GAAG,GAAG;YACnC,CAAC;IACH,GAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,YAAY;QACpD,aAAa,CAAC,OAAS,CAAC;gBACtB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;QAED,wDAAwD;QACxD,OAAO,aAAa,CAAC,IAAI,YAAY;IACvC,GAAG,EAAE;IAEL,qBACE,8OAAC;;0BACC,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBACC,gBAAgB;wBAChB,gBAAgB;;;;;;kCAElB,8OAAC;wBAAI,WAAU;;4BAGZ,8BACC,8OAAC;gCACC,QAAQ;gCACR,gBAAgB;gCAChB,eAAe;gCACf,SAAS;gCACT,kBAAmB,kBAAkB;;;;;uCAGvC,sEAAsE;4BACtE,CAAC,mCAAqB,8OAAC;0CAAE;;;;;qCAA8B,0CAA0C;;4BAKlG,mCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAiD;;;;;;;;;;;0CAIpE,8OAAC;gCACC,YAAY;gCACZ,SAAS;gCACT,kBAAkB;gCAClB,YAAY;gCACZ,cAAc;;;;;;;;;;;;;;;;;;YAInB,0CACC,8OAAC;gBACC,UAAU;gBACV,SAAS,IAAM,4BAA4B;gBAC3C,YAAY,CAAC,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC;gBACpD,kBAAkB;;;;;;;;;;;;AAK5B", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/node_modules/next/src/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactElement } from 'react'\nimport { BailoutToCSRError } from './bailout-to-csr'\n\ninterface BailoutToCSRProps {\n  reason: string\n  children: ReactElement\n}\n\n/**\n * If rendered on the server, this component throws an error\n * to signal Next.js that it should bail out to client-side rendering instead.\n */\nexport function BailoutToCSR({ reason, children }: BailoutToCSRProps) {\n  if (typeof window === 'undefined') {\n    throw new BailoutToCSRError(reason)\n  }\n\n  return children\n}\n"], "names": ["BailoutToCSR", "reason", "children", "window", "BailoutToCSRError"], "mappings": "AAAA;;;;;+BAcgBA,gBAAAA;;;eAAAA;;;8BAXkB;AAW3B,SAASA,aAAa,KAAuC;IAAvC,IAAA,EAAEC,MAAM,EAAEC,QAAQ,EAAqB,GAAvC;IAC3B,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,OAAA,cAA6B,CAA7B,IAAIC,cAAAA,iBAAiB,CAACH,SAAtB,qBAAA;mBAAA;wBAAA;0BAAA;QAA4B;IACpC;IAEA,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/node_modules/next/src/shared/lib/encode-uri-path.ts"], "sourcesContent": ["export function encodeURIPath(file: string) {\n  return file\n    .split('/')\n    .map((p) => encodeURIComponent(p))\n    .join('/')\n}\n"], "names": ["encodeURIPath", "file", "split", "map", "p", "encodeURIComponent", "join"], "mappings": ";;;;+BAAgBA,iBAAAA;;;eAAAA;;;AAAT,SAASA,cAAcC,IAAY;IACxC,OAAOA,KACJC,KAAK,CAAC,KACNC,GAAG,CAAC,CAACC,IAAMC,mBAAmBD,IAC9BE,IAAI,CAAC;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/node_modules/next/src/shared/lib/lazy-dynamic/preload-chunks.tsx"], "sourcesContent": ["'use client'\n\nimport { preload } from 'react-dom'\n\nimport { workAsyncStorage } from '../../../server/app-render/work-async-storage.external'\nimport { encodeURIPath } from '../encode-uri-path'\n\nexport function PreloadChunks({\n  moduleIds,\n}: {\n  moduleIds: string[] | undefined\n}) {\n  // Early return in client compilation and only load requestStore on server side\n  if (typeof window !== 'undefined') {\n    return null\n  }\n\n  const workStore = workAsyncStorage.getStore()\n  if (workStore === undefined) {\n    return null\n  }\n\n  const allFiles = []\n\n  // Search the current dynamic call unique key id in react loadable manifest,\n  // and find the corresponding CSS files to preload\n  if (workStore.reactLoadableManifest && moduleIds) {\n    const manifest = workStore.reactLoadableManifest\n    for (const key of moduleIds) {\n      if (!manifest[key]) continue\n      const chunks = manifest[key].files\n      allFiles.push(...chunks)\n    }\n  }\n\n  if (allFiles.length === 0) {\n    return null\n  }\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n\n  return (\n    <>\n      {allFiles.map((chunk) => {\n        const href = `${workStore.assetPrefix}/_next/${encodeURIPath(chunk)}${dplId}`\n        const isCss = chunk.endsWith('.css')\n        // If it's stylesheet we use `precedence` o help hoist with React Float.\n        // For stylesheets we actually need to render the CSS because nothing else is going to do it so it needs to be part of the component tree.\n        // The `preload` for stylesheet is not optional.\n        if (isCss) {\n          return (\n            <link\n              key={chunk}\n              // @ts-ignore\n              precedence=\"dynamic\"\n              href={href}\n              rel=\"stylesheet\"\n              as=\"style\"\n            />\n          )\n        } else {\n          // If it's script we use ReactDOM.preload to preload the resources\n          preload(href, {\n            as: 'script',\n            fetchPriority: 'low',\n          })\n          return null\n        }\n      })}\n    </>\n  )\n}\n"], "names": ["PreloadChunks", "moduleIds", "window", "workStore", "workAsyncStorage", "getStore", "undefined", "allFiles", "reactLoadableManifest", "manifest", "key", "chunks", "files", "push", "length", "dplId", "process", "env", "NEXT_DEPLOYMENT_ID", "map", "chunk", "href", "assetPrefix", "encodeURIPath", "isCss", "endsWith", "link", "precedence", "rel", "as", "preload", "fetchPriority"], "mappings": "AAAA;;;;;+BAOg<PERSON>,iBAAAA;;;eAAAA;;;;0BALQ;0CAES;+BACH;AAEvB,SAASA,cAAc,KAI7B;IAJ6B,IAAA,EAC5BC,SAAS,EAGV,GAJ6B;IAK5B,+EAA+E;IAC/E,IAAI,OAAOC,WAAW,aAAa;QACjC,OAAO;IACT;IAEA,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAC3C,IAAIF,cAAcG,WAAW;QAC3B,OAAO;IACT;IAEA,MAAMC,WAAW,EAAE;IAEnB,4EAA4E;IAC5E,kDAAkD;IAClD,IAAIJ,UAAUK,qBAAqB,IAAIP,WAAW;QAChD,MAAMQ,WAAWN,UAAUK,qBAAqB;QAChD,KAAK,MAAME,OAAOT,UAAW;YAC3B,IAAI,CAACQ,QAAQ,CAACC,IAAI,EAAE;YACpB,MAAMC,SAASF,QAAQ,CAACC,IAAI,CAACE,KAAK;YAClCL,SAASM,IAAI,IAAIF;QACnB;IACF;IAEA,IAAIJ,SAASO,MAAM,KAAK,GAAG;QACzB,OAAO;IACT;IAEA,MAAMC,QAAQC,QAAQC,GAAG,CAACC,kBAAkB,GACxC,AAAC,UAAOF,QAAQC,GAAG,CAACC,kBAAkB,IACtC;IAEJ,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBACGX,SAASY,GAAG,CAAC,CAACC;YACb,MAAMC,OAAUlB,UAAUmB,WAAW,GAAC,YAASC,CAAAA,GAAAA,eAAAA,aAAa,EAACH,SAASL;YACtE,MAAMS,QAAQJ,MAAMK,QAAQ,CAAC;YAC7B,wEAAwE;YACxE,0IAA0I;YAC1I,gDAAgD;YAChD,IAAID,OAAO;gBACT,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACE,QAAAA;oBAEC,aAAa;oBACbC,YAAW;oBACXN,MAAMA;oBACNO,KAAI;oBACJC,IAAG;mBALET;YAQX,OAAO;gBACL,kEAAkE;gBAClEU,CAAAA,GAAAA,UAAAA,OAAO,EAACT,MAAM;oBACZQ,IAAI;oBACJE,eAAe;gBACjB;gBACA,OAAO;YACT;QACF;;AAGN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/node_modules/next/src/shared/lib/lazy-dynamic/loadable.tsx"], "sourcesContent": ["import { Suspense, Fragment, lazy } from 'react'\nimport { BailoutToCSR } from './dynamic-bailout-to-csr'\nimport type { ComponentModule } from './types'\nimport { PreloadChunks } from './preload-chunks'\n\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule<P>(\n  mod: React.ComponentType<P> | ComponentModule<P> | undefined\n): {\n  default: React.ComponentType<P>\n} {\n  // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n  // Cases:\n  // mod: { default: Component }\n  // mod: Component\n  // mod: { default: proxy(Component) }\n  // mod: proxy(Component)\n  const hasDefault = mod && 'default' in mod\n  return {\n    default: hasDefault\n      ? (mod as ComponentModule<P>).default\n      : (mod as React.ComponentType<P>),\n  }\n}\n\nconst defaultOptions = {\n  loader: () => Promise.resolve(convertModule(() => null)),\n  loading: null,\n  ssr: true,\n}\n\ninterface LoadableOptions {\n  loader?: () => Promise<React.ComponentType<any> | ComponentModule<any>>\n  loading?: React.ComponentType<any> | null\n  ssr?: boolean\n  modules?: string[]\n}\n\nfunction Loadable(options: LoadableOptions) {\n  const opts = { ...defaultOptions, ...options }\n  const Lazy = lazy(() => opts.loader().then(convertModule))\n  const Loading = opts.loading\n\n  function LoadableComponent(props: any) {\n    const fallbackElement = Loading ? (\n      <Loading isLoading={true} pastDelay={true} error={null} />\n    ) : null\n\n    // If it's non-SSR or provided a loading component, wrap it in a suspense boundary\n    const hasSuspenseBoundary = !opts.ssr || !!opts.loading\n    const Wrap = hasSuspenseBoundary ? Suspense : Fragment\n    const wrapProps = hasSuspenseBoundary ? { fallback: fallbackElement } : {}\n    const children = opts.ssr ? (\n      <>\n        {/* During SSR, we need to preload the CSS from the dynamic component to avoid flash of unstyled content */}\n        {typeof window === 'undefined' ? (\n          <PreloadChunks moduleIds={opts.modules} />\n        ) : null}\n        <Lazy {...props} />\n      </>\n    ) : (\n      <BailoutToCSR reason=\"next/dynamic\">\n        <Lazy {...props} />\n      </BailoutToCSR>\n    )\n\n    return <Wrap {...wrapProps}>{children}</Wrap>\n  }\n\n  LoadableComponent.displayName = 'LoadableComponent'\n\n  return LoadableComponent\n}\n\nexport default Loadable\n"], "names": ["convertModule", "mod", "<PERSON><PERSON><PERSON><PERSON>", "default", "defaultOptions", "loader", "Promise", "resolve", "loading", "ssr", "Loadable", "options", "opts", "Lazy", "lazy", "then", "Loading", "LoadableComponent", "props", "fallbackElement", "isLoading", "past<PERSON>elay", "error", "hasSuspenseBoundary", "Wrap", "Suspense", "Fragment", "wrapProps", "fallback", "children", "window", "PreloadChunks", "moduleIds", "modules", "BailoutToCSR", "reason", "displayName"], "mappings": ";;;;+BA4EA,WAAA;;;eAAA;;;;uBA5EyC;qCACZ;+BAEC;AAE9B,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASA,cACPC,GAA4D;IAI5D,iHAAiH;IACjH,SAAS;IACT,8BAA8B;IAC9B,iBAAiB;IACjB,qCAAqC;IACrC,wBAAwB;IACxB,MAAMC,aAAaD,OAAO,aAAaA;IACvC,OAAO;QACLE,SAASD,aACJD,IAA2BE,OAAO,GAClCF;IACP;AACF;AAEA,MAAMG,iBAAiB;IACrBC,QAAQ,IAAMC,QAAQC,OAAO,CAACP,cAAc,IAAM;IAClDQ,SAAS;IACTC,KAAK;AACP;AASA,SAASC,SAASC,OAAwB;IACxC,MAAMC,OAAO;QAAE,GAAGR,cAAc;QAAE,GAAGO,OAAO;IAAC;IAC7C,MAAME,OAAAA,WAAAA,GAAOC,CAAAA,GAAAA,OAAAA,IAAI,EAAC,IAAMF,KAAKP,MAAM,GAAGU,IAAI,CAACf;IAC3C,MAAMgB,UAAUJ,KAAKJ,OAAO;IAE5B,SAASS,kBAAkBC,KAAU;QACnC,MAAMC,kBAAkBH,UAAAA,WAAAA,GACtB,CAAA,GAAA,YAAA,GAAA,EAACA,SAAAA;YAAQI,WAAW;YAAMC,WAAW;YAAMC,OAAO;aAChD;QAEJ,kFAAkF;QAClF,MAAMC,sBAAsB,CAACX,KAAKH,GAAG,IAAI,CAAC,CAACG,KAAKJ,OAAO;QACvD,MAAMgB,OAAOD,sBAAsBE,OAAAA,QAAQ,GAAGC,OAAAA,QAAQ;QACtD,MAAMC,YAAYJ,sBAAsB;YAAEK,UAAUT;QAAgB,IAAI,CAAC;QACzE,MAAMU,WAAWjB,KAAKH,GAAG,GAAA,WAAA,GACvB,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;gBAEG,OAAOqB,WAAW,cAAA,WAAA,GACjB,CAAA,GAAA,YAAA,GAAA,EAACC,eAAAA,aAAa,EAAA;oBAACC,WAAWpB,KAAKqB,OAAO;qBACpC;8BACJ,CAAA,GAAA,YAAA,GAAA,EAACpB,MAAAA;oBAAM,GAAGK,KAAK;;;2BAGjB,CAAA,GAAA,YAAA,GAAA,EAACgB,qBAAAA,YAAY,EAAA;YAACC,QAAO;sBACnB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACtB,MAAAA;gBAAM,GAAGK,KAAK;;;QAInB,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACM,MAAAA;YAAM,GAAGG,SAAS;sBAAGE;;IAC/B;IAEAZ,kBAAkBmB,WAAW,GAAG;IAEhC,OAAOnB;AACT;MAEA,WAAeP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/node_modules/next/src/shared/lib/app-dynamic.tsx"], "sourcesContent": ["import type React from 'react'\nimport type { JSX } from 'react'\nimport Loadable from './lazy-dynamic/loadable'\n\nimport type {\n  LoadableGeneratedOptions,\n  DynamicOptionsLoadingProps,\n  Loader,\n  LoaderComponent,\n} from './lazy-dynamic/types'\n\nexport {\n  type LoadableGeneratedOptions,\n  type DynamicOptionsLoadingProps,\n  type Loader,\n  type LoaderComponent,\n}\n\nexport type DynamicOptions<P = {}> = LoadableGeneratedOptions & {\n  loading?: () => JSX.Element | null\n  loader?: Loader<P>\n  loadableGenerated?: LoadableGeneratedOptions\n  modules?: string[]\n  ssr?: boolean\n}\n\nexport type LoadableOptions<P = {}> = DynamicOptions<P>\n\nexport type LoadableFn<P = {}> = (\n  opts: LoadableOptions<P>\n) => React.ComponentType<P>\n\nexport type LoadableComponent<P = {}> = React.ComponentType<P>\n\nexport default function dynamic<P = {}>(\n  dynamicOptions: DynamicOptions<P> | Loader<P>,\n  options?: DynamicOptions<P>\n): React.ComponentType<P> {\n  const loadableOptions: LoadableOptions<P> = {}\n\n  if (typeof dynamicOptions === 'function') {\n    loadableOptions.loader = dynamicOptions\n  }\n\n  const mergedOptions = {\n    ...loadableOptions,\n    ...options,\n  }\n\n  return Loadable({\n    ...mergedOptions,\n    modules: mergedOptions.loadableGenerated?.modules,\n  })\n}\n"], "names": ["dynamic", "dynamicOptions", "options", "mergedOptions", "loadableOptions", "loader", "Loadable", "modules", "loadableGenerated"], "mappings": ";;;;+BAk<PERSON>,WAAA;;;eAAwBA;;;;mEAhCH;AAgCN,SAASA,QACtBC,cAA6C,EAC7CC,OAA2B;QAehBC;IAbX,MAAMC,kBAAsC,CAAC;IAE7C,IAAI,OAAOH,mBAAmB,YAAY;QACxCG,gBAAgBC,MAAM,GAAGJ;IAC3B;IAEA,MAAME,gBAAgB;QACpB,GAAGC,eAAe;QAClB,GAAGF,OAAO;IACZ;IAEA,OAAOI,CAAAA,GAAAA,UAAAA,OAAQ,EAAC;QACd,GAAGH,aAAa;QAChBI,OAAO,EAAA,CAAEJ,mCAAAA,cAAcK,iBAAiB,KAAA,OAAA,KAAA,IAA/BL,iCAAiCI,OAAO;IACnD;AACF", "ignoreList": [0], "debugId": null}}]}